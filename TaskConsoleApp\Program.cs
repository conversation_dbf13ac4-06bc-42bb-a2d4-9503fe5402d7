using System.Collections.Generic;

class Program
{
    static void Main()
    {
        int[] numbers = { 1, 6, 6, 8, 4, 8, 8, 5, 8, 9, 6, 7 };

        Console.WriteLine("Vstupný zoznam čísel:");
        Console.WriteLine("{ " + string.Join(", ", numbers) + " }");
        Console.WriteLine();

   
        var frequency = CalculateFrequency(numbers);

        Console.WriteLine("Výstup - čísla s ich početnosťou:");
        foreach (var item in frequency)
        {
            Console.WriteLine($"{item.Key} – {item.Value}");
        }
    }


    static Dictionary<int, int> CalculateFrequency(int[] numbers)
    {
        var frequency = new Dictionary<int, int>();

        foreach (int number in numbers)
        {
            if (frequency.TryGetValue(number, out int count))
            {
                frequency[number] = count + 1;
            }
            else
            {
                frequency[number] = 1;
            }
        }

        return frequency;
    }
}
