// Program na vypísanie čísel s ich početnosťou
using System.Collections.Generic;

class Program
{
    static void Main()
    {
        // Uk<PERSON><PERSON><PERSON><PERSON> vstup
        int[] numbers = { 1, 6, 6, 8, 4, 8, 8, 5, 8, 9, 6, 7 };

        Console.WriteLine("Vstupný zoznam čísel:");
        Console.WriteLine("{ " + string.Join(", ", numbers) + " }");
        Console.WriteLine();

        // Vypočítanie početnosti čísel
        var frequency = CalculateFrequency(numbers);

        Console.WriteLine("Výstup - čísla s ich početnosťou:");
        foreach (var item in frequency)
        {
            Console.WriteLine($"{item.Key} – {item.Value}");
        }
    }

    /// <summary>
    /// Vypočíta početnosť čísel v poli a zachová poradie prvého výskytu
    /// </summary>
    /// <param name="numbers">Pole čísel</param>
    /// <returns>Dictionary s číslami a ich početnosťou v poradí prvého výskytu</returns>
    static Dictionary<int, int> CalculateFrequency(int[] numbers)
    {
        var frequency = new Dictionary<int, int>();

        foreach (int number in numbers)
        {
            if (frequency.TryGetValue(number, out int count))
            {
                frequency[number] = count + 1;
            }
            else
            {
                frequency[number] = 1;
            }
        }

        return frequency;
    }
}
